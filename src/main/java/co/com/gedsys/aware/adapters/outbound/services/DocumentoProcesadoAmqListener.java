package co.com.gedsys.aware.adapters.outbound.services;

import co.com.gedsys.aware.ports.models.Action;
import co.com.gedsys.aware.ports.models.ActionText;
import co.com.gedsys.aware.ports.models.ActionType;
import co.com.gedsys.aware.ports.models.Notification;
import co.com.gedsys.aware.ports.models.NotificationStatus;
import co.com.gedsys.aware.ports.models.NotificationType;
import co.com.gedsys.commons.constant.amqp.QueueName;
import co.com.gedsys.commons.events.process.DocumentoProcesado;
import co.com.gedsys.commons.interfaces.AbstractRabbitMQListener;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;

@Slf4j
@Service
public class DocumentoProcesadoAmqListener extends AbstractRabbitMQListener<String> {
    @Autowired
    private NotificationServices notificationServices;
    @Autowired
    private SimpMessagingTemplate simpMessagingTemplate;

    protected DocumentoProcesadoAmqListener(RabbitTemplate rabbitTemplate) {
        super(rabbitTemplate);
    }

    @RabbitListener(queues = QueueName.NOTIFICACIONES_DOCUMENTOS_PROCESADOS, containerFactory = "manualListenerContainerFactory")
    @Override
    public void processMessage(@Payload String payload, Message message, Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        super.processMessage(payload, message, channel, deliveryTag);
    }

    @Override
    protected void handleMessageProcessing(String message) throws IOException {
        var objectMapper = new ObjectMapper();
        var documentoProcesado = objectMapper.readValue(message, DocumentoProcesado.class);
        Notification notification = Notification.builder()
                .title(documentoProcesado.descripcion())
                .type(NotificationType.success)
                .status(NotificationStatus.unread)
                .timestamp(LocalDateTime.now())
                .action(new Action(
                        ActionType.link,
                        String.format(UIPathConstants.VER_DOCUMENTO, documentoProcesado.documentId()),
                        ActionText.VER_DOCUMENTO))
                .build();
        documentoProcesado.stakeholders().forEach(stakeholder -> {
            notification.setOwner(stakeholder);
            Notification notificationSaved = notificationServices.saveNotification(notification);
            simpMessagingTemplate.convertAndSend("/queue/" + stakeholder + "/notifications",
                    notificationSaved);
        });
    }
}

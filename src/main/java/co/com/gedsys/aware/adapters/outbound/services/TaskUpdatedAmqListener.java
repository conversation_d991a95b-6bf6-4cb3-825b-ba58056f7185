package co.com.gedsys.aware.adapters.outbound.services;

import co.com.gedsys.aware.ports.models.*;
import co.com.gedsys.commons.constant.amqp.QueueName;
import co.com.gedsys.commons.events.tasks.TaskEvent;
import co.com.gedsys.commons.interfaces.AbstractRabbitMQListener;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Slf4j
@Service
public class TaskUpdatedAmqListener extends AbstractRabbitMQListener<String> {
    private final ObjectMapper objectMapper;
    private final NotificationServices notificationServices;
    private final SimpMessagingTemplate simpMessagingTemplate;

    public TaskUpdatedAmqListener(RabbitTemplate rabbitTemplate,
            ObjectMapper objectMapper,
            NotificationServices notificationServices,
            SimpMessagingTemplate simpMessagingTemplate) {
        super(rabbitTemplate);
        this.objectMapper = objectMapper;
        this.notificationServices = notificationServices;
        this.simpMessagingTemplate = simpMessagingTemplate;
    }

    @RabbitListener(queues = { QueueName.TAREAS_ACTUALIZADAS }, containerFactory = "manualListenerContainerFactory")
    @Override
    public void processMessage(@Payload String payload, Message message, Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        super.processMessage(payload, message, channel, deliveryTag);
    }

    protected void handleMessageProcessing(String messageReceived) throws IOException {
        try {
            var taskEvent = objectMapper.readValue(messageReceived, TaskEvent.class);
            var notificationType = switch (taskEvent.type()) {
                case ASIGNADA -> processTaskEvent(taskEvent, "Tarea asignada", NotificationType.success);
                case VENCIDA -> processTaskEvent(taskEvent, "Tarea vencida", NotificationType.warning);
                case PROXIMA -> processTaskEvent(taskEvent, "Tarea próxima a vencer", NotificationType.info);
                default -> processTaskEvent(taskEvent, "Tarea", NotificationType.info);
            };
            log.debug("Procesada notificación de tarea tipo {} con resultado {}", taskEvent.type(), notificationType);
        } catch (Exception e) {
            log.error("Error procesando mensaje de tarea: {}", e.getMessage(), e);
            throw new IOException("Error procesando mensaje de tarea", e);
        }
    }

    private NotificationType processTaskEvent(TaskEvent message, String title, NotificationType type) {
        var notification = Notification.builder()
                .owner(message.assigned())
                .title(title)
                .type(type)
                .action(new Action(ActionType.link,
                        String.format(UIPathConstants.VER_TAREA, message.taskFormKey(), message.taskId()),
                        ActionText.VER_TAREA))
                .status(NotificationStatus.unread)
                .timestamp(message.timestamp())
                .details(message)
                .build();

        var notificationSaved = notificationServices.saveNotification(notification);
        var destination = "/queue/" + message.assigned() + "/notifications";
        simpMessagingTemplate.convertAndSend(destination, notificationSaved);
        
        log.debug("Enviada notificación a {} para tarea {}", destination, message.taskId());
        return type;
    }
}

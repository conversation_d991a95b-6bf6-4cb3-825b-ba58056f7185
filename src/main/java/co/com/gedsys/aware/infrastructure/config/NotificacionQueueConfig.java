package co.com.gedsys.aware.infrastructure.config;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import co.com.gedsys.commons.constant.amqp.QueueName;

@Configuration
public class NotificacionQueueConfig {

    // Constantes para la DLQ específica de notificaciones
    private static final String NOTIFICACIONES_DLX = "notificaciones.dlx";
    private static final String NOTIFICACIONES_DLQ = "notificaciones.dlq";
    private static final String NOTIFICACIONES_DLK = "notificaciones.dlk";

    /**
     * Dead Letter Exchange específico para notificaciones.
     * Este exchange manejará los mensajes fallidos de las colas de notificaciones.
     */
    @Bean(name = "notificacionesDeadLetterExchange")
    @ConditionalOnMissingBean(name = "notificacionesDeadLetterExchange")
    public TopicExchange notificacionesDeadLetterExchange() {
        return new TopicExchange(NOTIFICACIONES_DLX, true, false);
    }

    /**
     * Dead Letter Queue específica para notificaciones.
     * Esta cola almacenará los mensajes de notificaciones que han fallado en su procesamiento.
     */
    @Bean(name = "notificacionesDeadLetterQueue")
    @ConditionalOnMissingBean(name = "notificacionesDeadLetterQueue")
    public Queue notificacionesDeadLetterQueue() {
        return QueueBuilder.durable(NOTIFICACIONES_DLQ).build();
    }

    /**
     * Binding entre el Dead Letter Exchange y la Dead Letter Queue de notificaciones.
     */
    @Bean(name = "notificacionesDeadLetterBinding")
    @DependsOn({"notificacionesDeadLetterExchange", "notificacionesDeadLetterQueue"})
    @ConditionalOnMissingBean(name = "notificacionesDeadLetterBinding")
    public Binding notificacionesDeadLetterBinding(
            Queue notificacionesDeadLetterQueue,
            TopicExchange notificacionesDeadLetterExchange) {
        return BindingBuilder
                .bind(notificacionesDeadLetterQueue)
                .to(notificacionesDeadLetterExchange)
                .with(NOTIFICACIONES_DLK);
    }

    /**
     * Cola principal para notificaciones de documentos procesados.
     * Configurada con su propia Dead Letter Queue específica.
     */
    @Bean(name = "notificacionesDocumentosProcesadosQueue")
    @ConditionalOnMissingBean(name = "notificacionesDocumentosProcesadosQueue")
    Queue notificacionesDocumentosProcesadosQueue() {
        return QueueBuilder.durable(QueueName.NOTIFICACIONES_DOCUMENTOS_PROCESADOS)
                .deadLetterExchange(NOTIFICACIONES_DLX)
                .deadLetterRoutingKey(NOTIFICACIONES_DLK)
                .build();
    }
}

package co.com.gedsys.aware.infrastructure.config;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import co.com.gedsys.commons.constant.amqp.QueueName;
import co.com.gedsys.commons.constant.amqp.RoutingKeyName;

import static co.com.gedsys.commons.constant.amqp.DeadLetterConstant.DLX;

@Configuration
public class NotificacionQueueConfig {

    private static final String NOTIFICACIONES_DLQ = "notificaciones.dlq";
    private static final String NOTIFICACIONES_DLK = "notificaciones.dlk";

    @Bean(name = "notificacionesDeadLetterQueue")
    Queue notificacionesDeadLetterQueue() {
        return QueueBuilder.durable(NOTIFICACIONES_DLQ).build();
    }

    @Bean(name = "notificacionesDeadLetterBinding")
    @DependsOn({ "deadLetterExchange", "notificacionesDeadLetterQueue" })
    Binding notificacionesDeadLetterBinding(
            Queue notificacionesDeadLetterQueue,
            TopicExchange deadLetterExchange) {
        return BindingBuilder
                .bind(notificacionesDeadLetterQueue)
                .to(deadLetterExchange)
                .with(NOTIFICACIONES_DLK);
    }

    @Bean(name = "notificacionesDocumentosProcesadosQueue")
    Queue notificacionesDocumentosProcesadosQueue() {
        return QueueBuilder.durable(QueueName.NOTIFICACIONES_DOCUMENTOS_PROCESADOS)
                .deadLetterExchange(DLX)
                .deadLetterRoutingKey(NOTIFICACIONES_DLK)
                .build();
    }

    @Bean(name = "notificacionesDocumentosProcesadosBinding")
    @DependsOn({ "procesosTopicExchange", "notificacionesDocumentosProcesadosQueue" })
    Binding notificacionesDocumentosProcesadosBinding(
            Queue notificacionesDocumentosProcesadosQueue,
            TopicExchange procesosTopicExchange) {
        return BindingBuilder
                .bind(notificacionesDocumentosProcesadosQueue)
                .to(procesosTopicExchange)
                .with(RoutingKeyName.DOCUMENTO_PROCESADO);
    }

}

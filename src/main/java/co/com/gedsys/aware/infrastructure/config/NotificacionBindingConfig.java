package co.com.gedsys.aware.infrastructure.config;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import co.com.gedsys.commons.constant.amqp.RoutingKeyName;

@Configuration
public class NotificacionBindingConfig {

    @Bean(name = "notificacionesDocumentosProcesadosBinding")
    @ConditionalOnMissingBean(name = "notificacionesDocumentosProcesadosBinding")
    Binding notificacionesDocumentosProcesadosBinding(
            Queue notificacionesDocumentosProcesadosQueue,
            TopicExchange procesosTopicExchange) {
        return BindingBuilder
                .bind(notificacionesDocumentosProcesadosQueue)
                .to(procesosTopicExchange)
                .with(RoutingKeyName.DOCUMENTO_PROCESADO);
    }

}
